/**
 * Utility functions for handling time formatting without timezone conversion
 * These functions preserve the exact time values as stored in the database
 */

/**
 * Extract time (HH:MM) from an ISO datetime string without timezone conversion
 * @param isoString - ISO datetime string like "2025-07-26T09:00:00.000Z"
 * @returns Time string in HH:MM format like "09:00"
 */
export function extractTimeFromISO(isoString: string): string {
	// Extract the time portion directly from the ISO string
	// Format: "2025-07-26T09:00:00.000Z" -> "09:00"
	return isoString.substring(11, 16);
}

/**
 * Format time from ISO datetime string for display
 * @param isoString - ISO datetime string
 * @returns Formatted time string
 */
export function formatTimeFromISO(isoString: string): string {
	return extractTimeFromISO(isoString);
}

/**
 * Create a time range string from start and end ISO datetime strings
 * @param startISO - Start time ISO string
 * @param endISO - End time ISO string
 * @returns Time range string like "09:00-14:00"
 */
export function formatTimeRangeFromISO(startISO: string, endISO: string): string {
	const startTime = extractTimeFromISO(startISO);
	const endTime = extractTimeFromISO(endISO);
	return `${startTime}-${endTime}`;
}

/**
 * Legacy function for backward compatibility
 * Use this when you need to maintain existing behavior but want timezone-safe formatting
 */
export function safeFormatTime(dateTimeString: string): string {
	return extractTimeFromISO(dateTimeString);
}

/**
 * Create a timezone-safe ISO datetime string from date and time components
 * This preserves the local time as UTC, matching the format used by timeslot generation
 * @param date - Date string in YYYY-MM-DD format
 * @param time - Time string in HH:MM format
 * @returns ISO datetime string like "2025-07-26T10:00:00.000Z"
 */
export function createTimezoneSafeISO(date: string, time: string): string {
	return `${date}T${time}:00.000Z`;
}

/**
 * Parse a date string safely without timezone conversion
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Date object representing the date in local timezone
 */
export function parseDateSafely(dateString: string): Date {
	const [year, month, day] = dateString.split("-").map(Number);
	return new Date(year, month - 1, day);
}

/**
 * Extract date and time components from an ISO datetime string
 * @param isoString - ISO datetime string like "2025-07-26T10:00:00.000Z"
 * @returns Object with date and time components
 */
export function extractDateTimeComponents(isoString: string): { date: string; time: string; dateObj: Date } {
	const datePart = isoString.substring(0, 10); // YYYY-MM-DD
	const timePart = isoString.substring(11, 16); // HH:MM
	const dateObj = parseDateSafely(datePart);

	return {
		date: datePart,
		time: timePart,
		dateObj
	};
}
