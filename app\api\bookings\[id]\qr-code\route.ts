import { NextRequest, NextResponse } from "next/server";
import { supabaseAdmin } from "@/lib/supabase";
import { generateBookingQRCode, generateVerificationUrl } from "@/lib/qr-code";
import { appConfig } from "@/lib/env";

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const { id } = params;

		if (!id) {
			return NextResponse.json({ error: "Booking ID is required" }, { status: 400 });
		}

		// Fetch booking details
		const { data: reservation, error } = await supabaseAdmin
			.from("reservations")
			.select(
				`
        *,
        service:services(name),
        customer:customers(
          profile:profiles!customers_id_fkey(
            first_name,
            last_name,
            email,
            phone
          )
        )
      `
			)
			.eq("id", id)
			.single();

		if (error || !reservation) {
			return NextResponse.json({ error: "Booking not found" }, { status: 404 });
		}

		// Extract customer and service information
		const customer = reservation.customer.profile;
		const service = reservation.service;

		if (!customer || !service) {
			return NextResponse.json({ error: "Invalid booking data" }, { status: 400 });
		}

		// Format date and time using timezone-safe approach
		// Extract date and time directly from ISO string to avoid timezone conversion
		const startTimeISO = reservation.start_time;
		const datePart = startTimeISO.substring(0, 10); // YYYY-MM-DD
		const timePart = startTimeISO.substring(11, 16); // HH:MM

		// Parse date manually to avoid timezone issues
		const [year, month, day] = datePart.split("-").map(Number);
		const dateObj = new Date(year, month - 1, day);
		const dateString = dateObj.toLocaleDateString("fr-FR");
		const timeString = timePart;

		// Generate verification URL
		const verificationUrl = generateVerificationUrl(reservation.id, appConfig.url);

		// Generate QR code
		const qrCodeDataURL = await generateBookingQRCode({
			reservationId: reservation.id,
			reservationNumber: reservation.reservation_number,
			customerName: `${customer.first_name} ${customer.last_name}`,
			serviceName: service.name,
			date: dateString,
			time: timeString,
			participants: reservation.participant_count,
			totalAmount: reservation.total_amount,
			verificationUrl: verificationUrl,
		});

		return NextResponse.json({
			success: true,
			qrCode: qrCodeDataURL,
		});
	} catch (error) {
		console.error("Error generating QR code:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}
