import { supabase, supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const bookingId = params.id;

		console.log("=== BOOKING API ===");
		console.log("Booking ID:", bookingId);

		// Fetch reservation with related data
		const { data: reservation, error } = await (supabaseAdmin || supabase)
			.from("reservations")
			.select(
				`
        *,
        service:services (
          id,
          name,
          image_url,
          duration_minutes
        ),
        customer:customers (
          id,
          first_name,
          last_name,
          email,
          phone
        )
      `
			)
			.eq("id", bookingId)
			.single();

		console.log("Database query result:", {
			error: error ? error.message : null,
			reservation: !!reservation,
			reservationId: reservation?.id,
		});

		if (error) {
			console.log("Database error:", error);
			return NextResponse.json({ error: "Erreur de base de données" }, { status: 500 });
		}

		if (!reservation) {
			console.log("No reservation found with ID:", bookingId);
			return NextResponse.json({ error: "Réservation non trouvée" }, { status: 404 });
		}

		// Transform data for confirmation page using timezone-safe approach
		// Extract date and time directly from ISO string to avoid timezone conversion
		const startTimeISO = reservation.start_time;
		const datePart = startTimeISO.substring(0, 10); // YYYY-MM-DD
		const timePart = startTimeISO.substring(11, 16); // HH:MM
		const dateString = datePart;

		// Generate QR code for the booking
		let qrCodeDataURL = reservation.qr_code; // Fallback to stored QR code
		try {
			const { generateBookingQRCode, generateVerificationUrl } = await import("@/lib/qr-code");
			const { appConfig } = await import("@/lib/env");

			const verificationUrl = generateVerificationUrl(reservation.id, appConfig.url);
			qrCodeDataURL = await generateBookingQRCode({
				reservationId: reservation.id,
				reservationNumber: reservation.reservation_number,
				customerName: `${reservation.customer.first_name} ${reservation.customer.last_name}`,
				serviceName: reservation.service.name,
				date: dateString,
				time: timePart,
				participants: reservation.participant_count,
				totalAmount: reservation.total_amount,
				verificationUrl: verificationUrl,
			});
		} catch (error) {
			console.error("Error generating QR code:", error);
			// Use fallback QR code
		}

		const bookingData = {
			bookingNumber: reservation.reservation_number,
			customerName: `${reservation.customer.first_name} ${reservation.customer.last_name}`,
			email: reservation.customer.email,
			phone: reservation.customer.phone,
			bookingDate: reservation.created_at,
			totalAmount: reservation.total_amount,
			qrCode: qrCodeDataURL,
			items: [
				{
					service: reservation.service.name,
					date: dateString,
					time: timePart,
					participants: reservation.participant_count,
					price: reservation.total_amount,
					image: reservation.service.image_url || "/placeholder.svg",
				},
			],
		};

		return NextResponse.json({
			success: true,
			data: bookingData,
		});
	} catch (error) {
		console.error("Error fetching booking:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}
